import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.syner.biz',
  //appName: '商聯思維',
  appName: 'Synerthink',
  webDir: 'dist',
  ios: {
    handleApplicationNotifications: false
  },
  plugins: {
    Camera: {
      promptBeforeEnabling: true,
      promptMessage: "請允許使用相機以掃描QR碼"
    },
    CapacitorUpdater: {
      autoUpdate: false,  // Manual setup - we handle updates ourselves
      updateUrl: '',      // Not used in manual mode
      statsUrl: '',
      privateKey: '',
      version: '',
      directUpdate: false,     // We control when updates are applied
      resetWhenUpdate: true,   // Reset app state on update
      updateAvailable: false,  // We don't use automatic update events
      checkDelay: 0,          // Not used in manual mode
      localS3: false,
      localHost: '',
      localWebHost: 'localhost',
      localSupa: '',
      allowModifyUrl: true,
      defaultChannel: 'production'
    }
  }
};

export default config;
