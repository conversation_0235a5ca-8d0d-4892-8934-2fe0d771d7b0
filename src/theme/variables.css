/* Ionic Variables and Theming */

:root {
  /* Custom brand colors based on the mockup */
  --ion-color-primary: #6B4593;
  --ion-color-primary-rgb: 107, 69, 147;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #5e3d81;
  --ion-color-primary-tint: #7a589e;

  --ion-color-secondary: #FF1B85;
  --ion-color-secondary-rgb: 255, 27, 133;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #e01875;
  --ion-color-secondary-tint: #ff3291;

  --ion-color-tertiary: #9C27B0;
  --ion-color-tertiary-rgb: 156, 39, 176;
  --ion-color-tertiary-contrast: #ffffff;
  --ion-color-tertiary-contrast-rgb: 255, 255, 255;
  --ion-color-tertiary-shade: #89229b;
  --ion-color-tertiary-tint: #a63db8;

  --ion-color-success: #36B37E;
  --ion-color-success-rgb: 54, 179, 126;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #2f9d6f;
  --ion-color-success-tint: #4abb8b;

  --ion-color-warning: #FFAB00;
  --ion-color-warning-rgb: 255, 171, 0;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #e09600;
  --ion-color-warning-tint: #ffb31a;

  --ion-color-danger: #FF5630;
  --ion-color-danger-rgb: 255, 86, 48;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #e04c2a;
  --ion-color-danger-tint: #ff6745;

  --ion-color-dark: #42307D;
  --ion-color-dark-rgb: 66, 48, 125;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #3a2a6e;
  --ion-color-dark-tint: #55458a;

  --ion-color-medium: #8777B7;
  --ion-color-medium-rgb: 135, 119, 183;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb: 255, 255, 255;
  --ion-color-medium-shade: #7669a1;
  --ion-color-medium-tint: #9385be;

  --ion-color-light: #F4F5F7;
  --ion-color-light-rgb: 244, 245, 247;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb: 0, 0, 0;
  --ion-color-light-shade: #d7d8d9;
  --ion-color-light-tint: #f5f6f8;
  
  /* Custom Typography */
  --ion-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Dark theme overrides 
@media (prefers-color-scheme: dark) {
  body {
    --ion-background-color: #121212;
    --ion-background-color-rgb: 18,18,18;

    --ion-text-color: #ffffff;
    --ion-text-color-rgb: 255,255,255;

    --ion-border-color: #222222;

    --ion-color-step-50: #1e1e1e;
    --ion-color-step-100: #2a2a2a;
    --ion-color-step-150: #363636;
    --ion-color-step-200: #414141;
    --ion-color-step-250: #4d4d4d;
    --ion-color-step-300: #595959;
    --ion-color-step-350: #656565;
    --ion-color-step-400: #717171;
    --ion-color-step-450: #7d7d7d;
    --ion-color-step-500: #898989;
    --ion-color-step-550: #949494;
    --ion-color-step-600: #a0a0a0;
    --ion-color-step-650: #acacac;
    --ion-color-step-700: #b8b8b8;
    --ion-color-step-750: #c4c4c4;
    --ion-color-step-800: #d0d0d0;
    --ion-color-step-850: #dbdbdb;
    --ion-color-step-900: #e7e7e7;
    --ion-color-step-950: #f3f3f3;

    --ion-item-background: #1e1e1e;

    --ion-toolbar-background: #1f1f1f;

    --ion-tab-bar-background: #1f1f1f;

    --ion-card-background: #1e1e1e;
  }
}*/