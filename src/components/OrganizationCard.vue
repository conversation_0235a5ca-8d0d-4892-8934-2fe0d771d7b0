<template>
  <ion-card
    mode="ios"
    class="organization-card"
    button
    @click="goToOrganizationDetail"
  >
    <div class="organization-banner">
      <img
        :src="organization.banner || 'https://images.unsplash.com/photo-1517048676732-d65bc937f952'"
        :alt="organization.name"
        loading="lazy"
        decoding="async"
      />
      <div class="loading-placeholder"></div>
    </div>
    <ion-card-header>
      <div class="organization-info">
        <img
          class="organization-logo"
          :src="organization.logo || 'https://images.unsplash.com/photo-1522071820081-009f0129c71c'"
          :alt="organization.name"
          loading="lazy"
          decoding="async"
        />
        <div class="organization-details">
          <ion-card-title>{{ organization.name }}</ion-card-title>
          <div class="organization-meta">
            <span class="category-tag" v-if="organization.organization_categories">{{ organization.organization_categories.name }}</span>
          </div>
          <div class="organization-meta">
            <span class="branch-count">
              <ion-icon :icon="peopleOutline"></ion-icon>
              {{ organization.member_count || 0 }} 個分會
            </span>
            <span class="owner-name">
              <ion-icon :icon="personOutline"></ion-icon>
              {{ organization.owner?.full_name || '未知' }}
            </span>
          </div>
          <div class="organization-meta">
            <span class="created-time">
              <ion-icon :icon="timeOutline"></ion-icon>
              {{ formatTimeAgo(organization.created_at) }}
            </span>
          </div>
          <div class="organization-activity-level">
            <span :class="getActivityLevelClass(organization.activity_level)">
              {{ getActivityLevelText(organization.activity_level) }}
            </span>
          </div>
        </div>
      </div>
    </ion-card-header>
    <ion-card-content v-if="showJoinButton && isBranchOwner">
      <ion-button
        v-if="!isMember && !hasPendingApplication"
        size="small"
        expand="block"
        @click.stop.prevent="applyToOrganization(organization.id)"
      >
        <ion-icon :icon="addOutline" slot="start"></ion-icon>
        申請加入組織
      </ion-button>
      <ion-button
        v-else-if="!isMember && hasPendingApplication"
        size="small"
        expand="block"
        color="medium"
        disabled
      >
        <ion-icon :icon="timeOutline" slot="start"></ion-icon>
        申請審核中
      </ion-button>
    </ion-card-content>
  </ion-card>

  <ion-toast
    :is-open="!!toastMessage"
    :message="toastMessage"
    :duration="3000"
    @didDismiss="toastMessage = ''"
  ></ion-toast>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonIcon,
  IonButton,
  IonToast,
  alertController,
  loadingController,
} from '@ionic/vue';
import {
  peopleOutline,
  personOutline,
  timeOutline,
  addOutline,
} from 'ionicons/icons';
import { useAuthStore } from '@/stores/auth';
import { useOrganizationStore } from '@/stores/organization';
import { supabase } from '@/lib/supabase';
import { useRouter } from 'vue-router';
import { useAlert } from '@/composables/useAlert';

const props = defineProps({
  organization: {
    type: Object,
    required: true
  },
  isMember: {
    type: Boolean,
    default: false
  },
  showJoinButton: {
    type: Boolean,
    default: true
  },
  isBranchOwner: {
    type: Boolean,
    default: false
  }
});

const authStore = useAuthStore();
const organizationStore = useOrganizationStore();
const router = useRouter();
const toastMessage = ref('');
const { presentPrompt } = useAlert();
const hasPendingApplication = ref(false);

// Navigate to organization detail page
const goToOrganizationDetail = () => {
  // Save the current path in history state for proper back navigation
  const currentPath = router.currentRoute.value.path;
  router.push({
    path: `/organizations/${props.organization.id}`,
    // Store the current path in the state so we can use it for back navigation
    state: { back: currentPath }
  });
};

// Check if the branch has a pending application
onMounted(async () => {
  if (authStore.isAuthenticated && props.isBranchOwner) {
    // We would need to implement this with the actual branch ID
    // For now, we'll just set it to false
    hasPendingApplication.value = false;
  }
});

// Function to prompt for application reason and submit application
const promptForApplicationReason = async (organizationId: string) => {
  try {
    const { data, role } = await presentPrompt({
      header: '申請理由',
      message: '請簡單描述您申請加入的原因',
      inputs: [
        {
          name: 'reason',
          type: 'textarea',
          placeholder: '申請理由...'
        }
      ],
      buttons: [
        {
          text: '取消',
          role: 'cancel'
        },
        {
          text: '提交',
          role: 'confirm'
        }
      ]
    });

    if (role === 'confirm' && data?.values?.reason) {
      await submitApplication(organizationId, data.values.reason);
    }
  } catch (error) {
    console.error('Error showing reason prompt:', error);
    toastMessage.value = '操作失敗，請稍後再試';
  }
};

// Function to submit the application to the database
const submitApplication = async (organizationId: string, reason: string) => {
  try {
    const loading = await loadingController.create({});
    await loading.present();

    // We would need the actual branch ID here
    // For now, we'll use a placeholder
    const branchId = 'placeholder-branch-id';

    // Submit application to database
    await organizationStore.applyToOrganization(organizationId, branchId, reason);

    // Update local state
    hasPendingApplication.value = true;

    // Show success message
    toastMessage.value = '申請已提交，請等待審核';
  } catch (error) {
    console.error('Error submitting application:', error);
    toastMessage.value = '申請提交失敗，請稍後再試';
  } finally {
    loadingController.dismiss();
  }
};

// Apply to join an organization
const applyToOrganization = async (organizationId: string) => {
  if (!authStore.isAuthenticated) {
    toastMessage.value = '請先登入';
    return;
  }

  if (!props.isBranchOwner) {
    toastMessage.value = '只有分會長可以申請加入組織';
    return;
  }

  try {
    // Check if already applied
    if (hasPendingApplication.value) {
      toastMessage.value = '您已經申請加入此組織，請等待審核';
      return;
    }

    // Show prompt for application reason
    await promptForApplicationReason(organizationId);
  } catch (error) {
    console.error('Error in application process:', error);
    toastMessage.value = '操作失敗，請稍後再試';
  }
};

// Format time as "X time ago"
const formatTimeAgo = (dateString: string) => {
  if (!dateString) return '';

  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();

  // Convert to appropriate time unit
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  const diffMonth = Math.floor(diffDay / 30);
  const diffYear = Math.floor(diffMonth / 12);

  if (diffYear > 0) {
    return `${diffYear} 年前`;
  } else if (diffMonth > 0) {
    return `${diffMonth} 個月前`;
  } else if (diffDay > 0) {
    return `${diffDay} 天前`;
  } else if (diffHour > 0) {
    return `${diffHour} 小時前`;
  } else if (diffMin > 0) {
    return `${diffMin} 分鐘前`;
  } else {
    return '剛剛';
  }
};

// Get activity level text
const getActivityLevelText = (level: number) => {
  switch (level) {
    case 0:
      return '活躍度: 低';
    case 1:
      return '活躍度: 中';
    case 2:
      return '活躍度: 高';
    default:
      return '活躍度: 未知';
  }
};

// Get activity level CSS class
const getActivityLevelClass = (level: number) => {
  switch (level) {
    case 0:
      return 'activity-low';
    case 1:
      return 'activity-medium';
    case 2:
      return 'activity-high';
    default:
      return 'activity-unknown';
  }
};
</script>

<style scoped>
.organization-card {
  margin: 0;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

.organization-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.organization-banner {
  height: 140px;
  overflow: hidden;
  position: relative;
  background-color: #f5f5f5;
}

.organization-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
  will-change: transform;
  max-width: 100%;
}

@media (hover: hover) {
  .organization-card:hover .organization-banner img {
    transform: scale(1.05);
  }
}

.organization-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.organization-logo {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--ion-color-light);
  background-color: #f5f5f5;
  will-change: transform;
  max-width: 100%;
}

.organization-details {
  flex: 1;
}

.organization-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  margin-bottom: 4px;
}

.branch-count,
.owner-name,
.created-time {
  display: flex;
  align-items: center;
  gap: 4px;
}

.category-tag {
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
  background-color: rgba(var(--ion-color-tertiary-rgb), 0.15);
  color: var(--ion-color-tertiary);
}

.organization-activity-level {
  margin-top: 6px;
}

.organization-activity-level span {
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.activity-low {
  background-color: rgba(var(--ion-color-danger-rgb), 0.15);
  color: var(--ion-color-danger);
}

.activity-medium {
  background-color: rgba(var(--ion-color-warning-rgb), 0.15);
  color: var(--ion-color-warning-shade);
}

.activity-high {
  background-color: rgba(var(--ion-color-success-rgb), 0.15);
  color: var(--ion-color-success);
}

.activity-unknown {
  background-color: rgba(var(--ion-color-medium-rgb), 0.15);
  color: var(--ion-color-medium);
}

ion-card-header {
  padding: 12px 16px;
}

ion-card-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (max-width: 768px) {
  .organization-card:hover {
    transform: none;
  }

  .organization-card:hover .organization-banner img {
    transform: none;
  }
}
</style>
