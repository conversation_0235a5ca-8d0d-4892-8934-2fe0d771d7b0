import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { IonicVue } from '@ionic/vue';
import App from '@/App.vue';
import router from '@/router';
import { useAuthStore } from '@/stores/auth';
import { updateService } from '@/services/updateService';

/* Core CSS required for Ionic components to work properly */
import '@ionic/vue/css/core.css';

/* Basic CSS for apps built with Ionic */
import '@ionic/vue/css/normalize.css';
import '@ionic/vue/css/structure.css';
import '@ionic/vue/css/typography.css';

/* Optional CSS utils that can be commented out */
import '@ionic/vue/css/padding.css';
import '@ionic/vue/css/float-elements.css';
import '@ionic/vue/css/text-alignment.css';
import '@ionic/vue/css/text-transformation.css';
import '@ionic/vue/css/flex-utils.css';
import '@ionic/vue/css/display.css';

/* Theme variables */
import '@/theme/variables.css';
import '@/theme/style.css';

/* OneSignal */
import OneSignal from 'onesignal-cordova-plugin';

/* Notification Service */
import { notificationService } from '@/services/notificationService';

// PWA elements
import { defineCustomElements } from '@ionic/pwa-elements/loader';
defineCustomElements(window);

const pinia = createPinia();
const app = createApp(App);

app.use(IonicVue, { innerHTMLTemplatesEnabled: true });
app.use(pinia);
app.use(router);

// Initialize auth store
const authStore = useAuthStore();

// Initialize auth before mounting
authStore.initialize().then(() => {
  router.isReady().then(() => {
    app.mount('#app');

    // Initialize update service (it will handle its own update checking)
    updateService.initialize();

    // Enable verbose logging for debugging (remove in production)
    OneSignal.Debug.setLogLevel(6);
    // Initialize with your OneSignal App ID
    OneSignal.initialize("************************************");

    // Set up notification click handler for deep linking
    OneSignal.Notifications.addEventListener('click', (event: any) => {
      console.log('OneSignal notification clicked:', event);

      // Handle deep linking to chat conversations
      if (event.notification.additionalData?.conversationId) {
        const conversationId = event.notification.additionalData.conversationId;
        // Navigate to the specific conversation
        router.push(`/chat/${conversationId}`);
      }
    });

    // Use this method to prompt for push notifications.
    // We recommend removing this method after testing and instead use In-App Messages to prompt for notification permission.
    OneSignal.Notifications.requestPermission(true).then((accepted: boolean) => {
      console.log("User accepted notifications: " + accepted);

      // Set external user ID when user is authenticated
      if (authStore.isAuthenticated && authStore.currentUser?.id) {
        OneSignal.login(authStore.currentUser.id);
        console.log("OneSignal external user ID set:", authStore.currentUser.id);

        // Start notification queue processing
        notificationService.startProcessing();
      }
    });
  });
});