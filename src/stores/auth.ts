import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '@/lib/supabase';
import type { User } from '@supabase/supabase-js';
import { useUserStore } from './user';
import OneSignal from 'onesignal-cordova-plugin';
import { notificationService } from '@/services/notificationService';

export const useAuthStore = defineStore('auth', () => {
  const currentUser = ref<User | null>(null);
  const isLoading = ref(true);
  const userStore = useUserStore();

  const isAuthenticated = computed(() => !!currentUser.value && !!userStore.currentUser);

  // Fetch user profile with joined shop, branch, and liked shops data
  const fetchUserData = async (userId: any) => {
    return await supabase
      .from('users')
      .select(`
        *,
        avatar,
        shops:shops(*),
        branches:branches(*),
        userLikedShops:user_liked_shops(
          id,
          shop_id
        ),
        userLikedEvents:user_liked_events(
          id,
          event_id
        ),
        userLikedUsers:user_liked_users!user_liked_users_user_id_fkey(
          id,
          liked_user_id,
          notes
        ),
        referrer:referrer_id(id, username, full_name, role, avatar),
        branchApplications:branch_member_applications(
          *,
          branches (
            id,
            name,
            logo,
            description,
            district,
            category_id
          )
        ),
        branchMemberships:branch_members(
          *,
          branches (
            id,
            name,
            logo,
            description,
            district,
            category_id
          )
        )
      `)
      .eq('id', userId)
      .eq('branch_members.status', 'active')
      .maybeSingle();
  }

  // Initialize the auth state
  const initialize = async () => {
    try {
      // Get initial session
      const { data: { session } } = await supabase.auth.getSession();

      if (session?.user) {
        // Set the auth user first
        currentUser.value = session.user;

        try {
          // Fetch user profile with joined shop, branch, and liked shops data
          const { data: userData, error } = await fetchUserData(session.user.id);

          if (error) {
            console.error('Error fetching user profile:', error.message);
            currentUser.value = null;
            userStore.logout();
            return;
          }

          if (!userData) {
            console.error('No user profile found. User may need to complete registration.');
            currentUser.value = null;
            userStore.logout();
            return;
          }

          // If we have user data, proceed with login
          userStore.login(userData);
        } catch (profileError) {
          console.error('Unexpected error fetching user profile:', profileError);
          currentUser.value = null;
          userStore.logout();
        }
      } else {
        currentUser.value = null;
        userStore.logout();
      }

      // Listen for auth changes
      supabase.auth.onAuthStateChange(async (event, session) => {
        setTimeout(async () => {
          if (event === 'SIGNED_IN' && session?.user) {
            try {
              // Set the auth user first
              currentUser.value = session.user;

              // Fetch user profile with joined shop, branch, and liked shops data
              const { data: userData, error } = await fetchUserData(session.user.id);

              if (error) {
                console.error('Error fetching user profile:', error.message);
                currentUser.value = null;
                userStore.logout();
                return;
              }

              if (!userData) {
                console.error('No user profile found. User may need to complete registration.');
                currentUser.value = null;
                userStore.logout();
                return;
              }

              // If we have user data, proceed with login
              userStore.login(userData);

              // Set OneSignal external user ID for push notifications
              try {
                OneSignal.login(session.user.id);
                console.log('OneSignal external user ID set:', session.user.id);

                // Start notification queue processing
                notificationService.startProcessing();
              } catch (oneSignalError) {
                console.error('Error setting OneSignal external user ID:', oneSignalError);
              }
            } catch (profileError) {
              console.error('Unexpected error fetching user profile:', profileError);
              currentUser.value = null;
              userStore.logout();
            }
          } else if (event === 'SIGNED_OUT') {
            currentUser.value = null;
            userStore.logout();

            // Logout from OneSignal and stop notification processing
            try {
              OneSignal.logout();
              notificationService.stopProcessing();
              console.log('OneSignal user logged out and notification processing stopped');
            } catch (oneSignalError) {
              console.error('Error logging out from OneSignal:', oneSignalError);
            }
          }
        }, 0)
      });
    } catch (error) {
      console.error('Error initializing auth:', error);
      currentUser.value = null;
      userStore.logout();
    } finally {
      isLoading.value = false;
    }
  };

  const signOut = async () => {
    try {
      await supabase.auth.signOut();
      currentUser.value = null;
      userStore.logout();

      // Logout from OneSignal and stop notification processing
      try {
        OneSignal.logout();
        notificationService.stopProcessing();
        console.log('OneSignal user logged out and notification processing stopped');
      } catch (oneSignalError) {
        console.error('Error logging out from OneSignal:', oneSignalError);
      }
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // Function to refresh user data without setting up new listeners
  const refreshUserData = async () => {
    try {
      // Get current session
      const { data: { session } } = await supabase.auth.getSession();

      if (session?.user) {
        const { data: userData, error } = await fetchUserData(session.user.id);

        if (error) {
          console.error('Error refreshing user data:', error.message);
          return;
        }

        if (!userData) {
          console.error('No user profile found when refreshing data.');
          return;
        }

        // Update user data in the store
        userStore.login(userData);
      }
    } catch (error) {
      console.error('Error refreshing user data:', error);
    }
  };

  return {
    currentUser,
    isAuthenticated,
    isLoading,
    initialize,
    refreshUserData,
    signOut
  };
});