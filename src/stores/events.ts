import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '@/lib/supabase';
import type { Event } from '@/services/schema';
import { EventSchema, } from '@/services/schema';

export const useEventsStore = defineStore('events', () => {
  const events = ref<Event[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // Get upcoming events (events with dates in the future)
  const upcomingEvents = computed(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to beginning of day for accurate comparison
    
    return events.value
      .filter(event => new Date(event.start_datetime).getTime() >= today.getTime())
      .sort((a, b) => new Date(a.start_datetime).getTime() - new Date(b.start_datetime).getTime());
  });

  // Get past events (events with dates in the past)
  const pastEvents = computed(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to beginning of day for accurate comparison
    
    return events.value
      .filter(event => new Date(event.start_datetime).getTime() < today.getTime())
      .sort((a, b) => new Date(b.start_datetime).getTime() - new Date(a.start_datetime).getTime()); // Sort by most recent first
  });

  // Fetch all events from the database
  const fetchEvents = async () => {
    isLoading.value = true;
    error.value = null;
    
    try {
      const { data, error: supabaseError } = await supabase
        .from('events')
        .select('*')
        .order('date', { ascending: true });
      
      if (supabaseError) throw supabaseError;
      
      // Validate and transform the data
      const validatedData = data.map(event => EventSchema.parse(event));
      events.value = validatedData;
    } catch (err: any) {
      console.error('Error fetching events:', err);
      error.value = err.message || 'Failed to fetch events';
    } finally {
      isLoading.value = false;
    }
  };

  return {
    events,
    isLoading,
    error,
    upcomingEvents,
    pastEvents,
    fetchEvents
  };
});
