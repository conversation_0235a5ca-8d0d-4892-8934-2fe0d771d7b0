import { z } from 'zod';

// Common timestamp fields
const timestampFields = {
  created_at: z.string().nullable().transform(val => val ? new Date(val).toISOString() : null),
  updated_at: z.string().nullable().transform(val => val ? new Date(val).toISOString() : null)
};

// User schema
export const UserSchema = z.object({
  id: z.string().uuid(),
  username: z.string().min(1),
  full_name: z.string().min(1),
  email: z.string().email(),
  phone: z.string().nullable(),
  role: z.enum(['free', 'merchant', 'president']),
  referrer_id: z.string().uuid().nullable(),
  industry: z.string().nullable(),
  company_name: z.string().nullable(),
  user_number: z.number().nullable(),
  intro: z.string().nullable(),
  ...timestampFields
});

// Product schema
export const ProductSchema = z.object({
  id: z.string().uuid(),
  merchant_id: z.string().uuid(),
  name: z.string().min(1),
  description: z.string(),
  price: z.number().positive(),
  profit_sharing_rate: z.number().min(0).max(1),
  cover_photo: z.string().url(),
  product_images: z.array(z.string().url()),
  merchant: z.string(),
  ...timestampFields
});

// Event schema
export const EventSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid().nullable(), // Updated to allow null
  title: z.string().min(1),
  description: z.string(),
  address: z.string().min(1),
  banner_photo: z.string().nullable(),
  max_participants: z.coerce.number().positive().nullable(),
  start_datetime: z.string(),
  end_datetime: z.string(),
  ...timestampFields
});

// Event creation schema (without id and timestamps)
export const CreateEventSchema = EventSchema.omit({
  id: true,
  created_at: true,
  updated_at: true
}).extend({
  start_datetime: z.string().refine(
    (datetime) => new Date(datetime) > new Date(),
    { message: "Event start date/time must be in the future" }
  ),
  end_datetime: z.string()
}).refine(
  (data) => {
    const startDateTime = new Date(data.start_datetime);
    const endDateTime = new Date(data.end_datetime);
    return endDateTime > startDateTime;
  },
  { message: "End date/time must be after start date/time" }
);

// Shop schema
export const ShopSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  description: z.string().nullable(),
  logo: z.string().nullable(),
  banner: z.string().nullable(),
  website: z.string().nullable(),
  is_featured: z.boolean().nullable(),
  notification_emails: z.array(z.string()).nullable(),
  notification_group: z.string().nullable(),
  owner_id: z.string().uuid(),
  rating: z.number().nullable(),
  rating_count: z.number().nullable(),
  shop_category_id: z.number().nullable(),
  product_count: z.number().nullable(),
  like_count: z.number().nullable(),

  // Required profile fields
  business_type: z.string().nullable(),
  main_products_services: z.array(z.string()).nullable(),
  service_area: z.string().nullable(),
  contact_phone: z.string().nullable(),
  contact_email: z.string().nullable(),
  physical_address: z.string().nullable(),
  operating_hours: z.string().nullable(),
  languages_supported: z.array(z.string()).nullable(),
  payment_methods: z.array(z.string()).nullable(),
  business_years: z.string().nullable(),
  product_service_specs: z.string().nullable(),
  return_policy: z.string().nullable(),
  business_registration_number: z.string().nullable(),
  emergency_contact_name: z.string().nullable(),
  emergency_contact_phone: z.string().nullable(),
  shipping_delivery_info: z.string().nullable(),
  target_market: z.string().nullable(),
  keywords: z.array(z.string()).nullable(),

  // Optional profile fields
  detailed_description: z.string().nullable(),
  customization_options: z.string().nullable(),
  trial_samples: z.string().nullable(),
  maintenance_service: z.string().nullable(),
  delivery_logistics: z.string().nullable(),
  product_language_labels: z.string().nullable(),
  installation_service: z.string().nullable(),
  wholesale_pricing: z.string().nullable(),
  target_customers: z.string().nullable(),
  customer_sources: z.string().nullable(),
  collaboration_willingness: z.string().nullable(),
  revenue_sharing_model: z.string().nullable(),
  collaboration_review_process: z.string().nullable(),
  agency_franchise_opportunities: z.string().nullable(),

  ...timestampFields
});

// Bonus record schema
export const BonusRecordSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  amount: z.number(),
  source: z.string(),
  transaction_date: z.string().transform(val => new Date(val).toISOString()),
  ...timestampFields
});

export type User = z.infer<typeof UserSchema>;
export type Product = z.infer<typeof ProductSchema>;
export type Event = z.infer<typeof EventSchema>;
export type CreateEvent = z.infer<typeof CreateEventSchema>;
export type Shop = z.infer<typeof ShopSchema>;
export type BonusRecord = z.infer<typeof BonusRecordSchema>;