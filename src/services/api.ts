import { supabase } from '@/lib/supabase';
import { UserSchema, ProductSchema, EventSchema, BonusRecordSchema, CreateEventSchema, } from './schema';
import type { User, Product, Event, BonusRecord, CreateEvent } from './schema';
import { z } from 'zod';

class ApiService {
  private static instance: ApiService;
  
  private constructor() {}
  
  static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  // User methods
  async getUser(id: string): Promise<User> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    UserSchema.parse(data); // Validate the raw data
    return data;
  }

  async verifyReferralCode(code: string): Promise<{ id: string; username: string; fullName: string } | null> {
    const { data, error } = await supabase
      .rpc('verify_referral_code', { code });

    if (error) throw error;
    if (!data || data.length === 0) return null;

    return {
      id: data[0].id,
      username: data[0].username,
      fullName: data[0].full_name
    };
  }

  // Product methods
  async getProducts(): Promise<Product[]> {
    const { data, error } = await supabase
      .from('products')
      .select('*');
    
    if (error) throw error;
    z.array(ProductSchema).parse(data); // Validate the raw data
    return data;
  }

  // Event methods
  async getEvents(): Promise<Event[]> {
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .order('start_datetime', { ascending: true });
    if (error) throw error;
    z.array(EventSchema).parse(data); // Validate the raw data
    return data;
  }

  async createEvent(eventData: CreateEvent): Promise<Event> {
    // Validate the input data
    CreateEventSchema.parse(eventData);

    const { data, error } = await supabase
      .from('events')
      .insert([eventData])
      .select()
      .single();

    if (error) throw error;
    EventSchema.parse(data); // Validate the response data
    return data;
  }

  // Bonus methods
  async getBonusRecords(userId: string): Promise<BonusRecord[]> {
    const { data, error } = await supabase
      .from('bonus_records')
      .select('*')
      .eq('user_id', userId);
    
    if (error) throw error;
    z.array(BonusRecordSchema).parse(data); // Validate the raw data
    return data;
  }
}

export const api = ApiService.getInstance();