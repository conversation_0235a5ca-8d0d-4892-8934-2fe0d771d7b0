<template>
  <ion-page>
    <ion-tabs>
      <ion-router-outlet></ion-router-outlet>
      <ion-tab-bar color="primary" slot="bottom">
        <ion-tab-button tab="home" href="/home">
          <ion-icon :icon="home" />
          <ion-label>首頁</ion-label>
        </ion-tab-button>

        <ion-tab-button tab="products" href="/products">
          <ion-icon :icon="cart" />
          <ion-label>產品</ion-label>
        </ion-tab-button>

        <ion-tab-button tab="shops" href="/shops">
          <ion-icon :icon="storefront" />
          <ion-label>商家</ion-label>
        </ion-tab-button>

        <ion-tab-button tab="branches" :href="branchesTabHref">
          <ion-icon :icon="people" />
          <ion-label>分會</ion-label>
        </ion-tab-button>

        <ion-tab-button tab="events" href="/events">
          <ion-icon :icon="calendar" />
          <ion-label>活動</ion-label>
        </ion-tab-button>

        <!--<ion-tab-button tab="bonus" href="/bonus">
          <ion-icon :icon="wallet" />
          <ion-label>奬金</ion-label>
        </ion-tab-button>-->

        <ion-tab-button tab="profile" href="/profile">
          <ion-icon :icon="person" />
          <ion-label>個人</ion-label>
        </ion-tab-button>
      </ion-tab-bar>
    </ion-tabs>
  </ion-page>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import {
  IonTabBar,
  IonTabButton,
  IonTabs,
  IonLabel,
  IonIcon,
  IonPage,
  IonRouterOutlet
} from '@ionic/vue';
import {
  home,
  cart,
  storefront,
  calendar,
  person,
  wallet,
  people,
} from 'ionicons/icons';

const route = useRoute();

// Keep the branches tab active when on the organizations page
const branchesTabHref = computed(() => {
  // If we're already on the organizations page, don't change the URL
  if (route.path === '/organizations') {
    return '/organizations';
  }
  return '/branches';
});
</script>