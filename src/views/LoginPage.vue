<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/home" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>登入</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <div class="auth-container">
        <div class="auth-card">
          <div class="brand-section">
            <LogoImg className="brand-logo" />
            <h1 class="brand-title">Synerthink</h1>
            <p class="brand-subtitle">共享生態聯盟</p>
          </div>

          <form @submit.prevent="handleLogin" class="login-form">
            <div class="form-header">
              <h2>歡迎回來</h2>
              <p>請登入您的帳戶</p>
            </div>

            <ion-list>
              <ion-item class="custom-item">
                <ion-icon :icon="personOutline" slot="start"></ion-icon>
                <ion-input
                  v-model="formData.identifier"
                  label="登入帳號或電郵地址"
                  label-placement="floating"
                  type="text"
                  required
                >
                </ion-input>
              </ion-item>

              <ion-item class="custom-item">
                <ion-icon :icon="lockClosedOutline" slot="start"></ion-icon>
                <ion-input
                  v-model="formData.password"
                  label="密碼"
                  label-placement="floating"
                  type="password"
                  required
                >
                </ion-input>
              </ion-item>
            </ion-list>

            <div class="form-actions">
              <ion-button type="submit" expand="block" class="submit-button" :disabled="isLoading">
                {{ isLoading ? '登入中...' : '登入' }}
                <ion-icon slot="end" :icon="logInOutline"></ion-icon>
              </ion-button>

              <div class="additional-actions">
                <ion-button fill="clear" size="small" @click="showForgotPasswordModal = true" class="forgot-password">
                  忘記密碼？
                </ion-button>
              </div>

              <div class="register-prompt">
                <span>還沒有帳戶？</span>
                <ion-button fill="clear" router-link="/register" class="register-link">
                  立即註冊
                  <ion-icon slot="end" :icon="arrowForward"></ion-icon>
                </ion-button>
              </div>
            </div>
          </form>
        </div>
      </div>

      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>

      <!-- Forgot Password Modal -->
      <ion-modal :is-open="showForgotPasswordModal" @didDismiss="showForgotPasswordModal = false">
        <ion-header>
          <ion-toolbar>
            <ion-title>重設密碼</ion-title>
            <ion-buttons slot="end">
              <ion-button @click="showForgotPasswordModal = false">關閉</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <div class="forgot-password-container">
            <h2>忘記密碼</h2>
            <p>請輸入您的電郵地址，我們將發送重設密碼的連結給您。</p>

            <form @submit.prevent="handleForgotPassword">
              <ion-item class="custom-item">
                <ion-icon :icon="mailOutline" slot="start"></ion-icon>
                <ion-input
                  v-model="resetEmail"
                  label="電郵地址"
                  label-placement="floating"
                  type="email"
                  required
                >
                </ion-input>
              </ion-item>

              <ion-button
                type="submit"
                expand="block"
                class="submit-button"
                :disabled="isResettingPassword"
              >
                {{ isResettingPassword ? '發送中...' : '發送重設連結' }}
              </ion-button>
            </form>
          </div>
        </ion-content>
      </ion-modal>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { IonPage, IonContent, IonList, IonItem, IonInput, IonButton, IonIcon, IonToast, IonHeader, IonToolbar, IonTitle, IonButtons, IonBackButton, IonModal } from '@ionic/vue';
import { personOutline, lockClosedOutline, logInOutline, arrowForward, mailOutline } from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useUserStore } from '@/stores/user';
import { useAuthStore } from '@/stores/auth';
import LogoImg from '@/components/LogoImg.vue';

const router = useRouter();
const userStore = useUserStore();
const authStore = useAuthStore();
const toastMessage = ref('');
const isLoading = ref(false);
const showForgotPasswordModal = ref(false);
const resetEmail = ref('');
const isResettingPassword = ref(false);

const formData = ref({
  identifier: '',
  password: ''
});

// Redirect if already authenticated
onMounted(() => {
  if (authStore.isAuthenticated) {
    router.push('/home');
  }
});

const clearForm = () => {
  formData.value = {
    identifier: '',
    password: ''
  };
};

const handleLogin = async () => {
  if (isLoading.value) return;

  try {
    isLoading.value = true;

    // First try to find user by username if the input doesn't look like an email
    let loginEmail = formData.value.identifier;
    if (!loginEmail.includes('@')) {
      const { data: userData } = await supabase
        .from('user_login_info')
        .select('email')
        .eq('username', formData.value.identifier)
        .maybeSingle();

      if (userData?.email) {
        loginEmail = userData.email;
      }
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email: loginEmail,
      password: formData.value.password
    });

    if (error) {
      toastMessage.value = '登入失敗，請檢查帳號和密碼';
      return;
    }

    if (data.user) {
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select(`
          *,
          shop:shops(*),
          branch:branches(*)
        `)
        .eq('id', data.user.id)
        .single();

      if (userError) throw userError;

      userStore.login(userData);
      clearForm();
      router.push('/home');
    }
  } catch (error) {
    console.error('Login error:', error);
    toastMessage.value = '登入失敗，請稍後再試';
  } finally {
    isLoading.value = false;
  }
};

const handleForgotPassword = async () => {
  if (!resetEmail.value || isResettingPassword.value) return;

  try {
    isResettingPassword.value = true;

    const { error } = await supabase.auth.resetPasswordForEmail(resetEmail.value, {
      redirectTo: `https://syner-biz.com/reset-password`,
    });

    if (error) {
      throw error;
    }

    // Close the modal and show success message
    showForgotPasswordModal.value = false;
    resetEmail.value = '';
    toastMessage.value = '密碼重設連結已發送到您的電郵地址';
  } catch (error) {
    console.error('Password reset error:', error);
    toastMessage.value = '發送重設連結時發生錯誤，請稍後再試';
  } finally {
    isResettingPassword.value = false;
  }
};
</script>

<style scoped>
.auth-container {
  min-height: 100vh;
  display: flex;
  /*align-items: center;*/
  justify-content: center;
  padding: 0;
}

.auth-card {
  width: 100%;
  max-width: 500px;
  border-radius: 24px;
  padding: 1rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  background: white;
}

.brand-section {
  text-align: center;
  margin-bottom: 2.5rem;
}

.brand-logo {
  width: 120px;
  margin: 0 auto 1rem;
}

.brand-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--ion-color-primary);
  margin: 0;
  line-height: 1.2;
}

.brand-subtitle {
  color: var(--ion-color-medium);
  font-size: 1.1rem;
  margin-top: 0.5rem;
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: var(--ion-text-color);
}

.form-header p {
  color: var(--ion-color-medium);
  margin: 0.5rem 0 0;
}

.custom-item {
  --padding-start: 0;
  --inner-padding-end: 0;
  --background: transparent;
  margin-bottom: 1.5rem;
  --border-color: var(--ion-color-medium);
  --border-style: solid;
  --border-width: 1px;
  --border-radius: 8px;
  --highlight-height: 0;
}

.custom-item::part(native) {
  padding: 0.75rem 1rem;
}

ion-input {
  --padding-start: 2.5rem;
  font-size: 1rem;
}

ion-icon {
  font-size: 1.25rem;
  color: var(--ion-color-medium);
}

.form-actions {
  margin-top: 2rem;
}

.submit-button {
  --background: var(--ion-color-primary);
  --border-radius: 8px;
  --box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), 0.3);
  font-weight: 600;
  font-size: 1.1rem;
  height: 48px;
  margin: 0;
  text-transform: none;
}

.submit-button:disabled {
  --background: var(--ion-color-medium);
  --box-shadow: none;
  opacity: 0.7;
}

.additional-actions {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.forgot-password {
  --color: var(--ion-color-medium);
  font-weight: 500;
  font-size: 0.9rem;
}

.register-prompt {
  margin-top: 2rem;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.register-prompt span {
  color: var(--ion-color-medium);
}

.register-link {
  --color: var(--ion-color-primary);
  font-weight: 600;
  text-transform: none;
}

/* Hover effects */
.submit-button:not(:disabled):hover {
  --background: var(--ion-color-primary-shade);
}

.custom-item:hover {
  --border-color: var(--ion-color-primary);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-card {
  animation: fadeInUp 0.6s ease-out;
}

.brand-section {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.form-header {
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.login-form ion-item {
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

.form-actions {
  animation: fadeInUp 0.6s ease-out 0.5s both;
}

/* Forgot Password Modal Styles */
.forgot-password-container {
  padding: 1.5rem;
  max-width: 500px;
  margin: 0 auto;
}

.forgot-password-container h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--ion-text-color);
}

.forgot-password-container p {
  color: var(--ion-color-medium);
  margin-bottom: 2rem;
}

.forgot-password-container .submit-button {
  margin-top: 2rem;
}
</style>