<template>
  <ion-page>
    <ion-header>
      <!-- Segment to switch between branches and organizations -->
      <ion-toolbar>
        <ion-segment v-model="viewMode" mode="ios" @ionChange="handleViewModeChange">
          <ion-segment-button value="branches">
            <ion-label>分會</ion-label>
          </ion-segment-button>
          <ion-segment-button value="organizations">
            <ion-label>組織</ion-label>
          </ion-segment-button>
        </ion-segment>
      </ion-toolbar>

      <!-- Search and Filter Toolbar -->
      <ion-toolbar class="filter-toolbar">
        <ion-searchbar
          v-model="searchQuery"
          :placeholder="viewMode === 'branches' ? '搜尋分會' : '搜尋組織'"
          @ionInput="handleSearch"
          class="custom-searchbar"
        ></ion-searchbar>

        <ion-buttons slot="end">
          <ion-button v-if="authStore.isAuthenticated" :router-link="viewMode === 'branches' ? '/liked-branches' : '/liked-organizations'">
            <ion-icon :icon="heart" slot="icon-only"></ion-icon>
          </ion-button>
          <ion-button class="sort-button" @click="presentSortOptions($event)" v-if="viewMode === 'branches' || viewMode === 'organizations'">
            <ion-icon :icon="options" slot="icon-only"></ion-icon>
          </ion-button>
        </ion-buttons>

        <!-- Sort Popover - Single popover for both views -->
        <ion-popover :is-open="showSortPopover" :event="popoverEvent" @didDismiss="showSortPopover = false">
          <ion-content>
            <ion-list lines="full">
              <ion-item button @click="setSortOption('newest')" detail="false">
                <ion-label>最新創建</ion-label>
                <ion-icon v-if="sortOption === 'newest'" :icon="checkmark" slot="end"></ion-icon>
              </ion-item>
              <ion-item button @click="setSortOption('oldest')" detail="false">
                <ion-label>最早創建</ion-label>
                <ion-icon v-if="sortOption === 'oldest'" :icon="checkmark" slot="end"></ion-icon>
              </ion-item>
              <ion-item button @click="setSortOption('popular')" detail="false">
                <ion-label>{{ viewMode === 'branches' ? '最多成員' : '最多分會' }}</ion-label>
                <ion-icon v-if="sortOption === 'popular'" :icon="checkmark" slot="end"></ion-icon>
              </ion-item>
              <ion-item button @click="setSortOption('name')" detail="false">
                <ion-label>名稱排序</ion-label>
                <ion-icon v-if="sortOption === 'name'" :icon="checkmark" slot="end"></ion-icon>
              </ion-item>
            </ion-list>
          </ion-content>
        </ion-popover>
      </ion-toolbar>

      <!-- Filter Options -->
      <ion-toolbar class="filter-options">
        <div class="filter-dropdowns">
          <ion-item v-if="viewMode === 'branches'" lines="none" class="filter-dropdown">
            <ion-select
              v-model="selectedCategory"
              interface="popover"
              :placeholder="'全部類別'"
              @ionChange="handleCategoryChange"
            >
              <ion-select-option value="all">全部類別</ion-select-option>
              <ion-select-option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.title }}
              </ion-select-option>
            </ion-select>
          </ion-item>

          <ion-item v-if="viewMode === 'branches'" lines="none" class="filter-dropdown">
            <ion-select
              v-model="selectedSource"
              interface="popover"
              :placeholder="'來自'"
              @ionChange="handleSourceChange"
            >
              <ion-select-option value="all">任何來源</ion-select-option>
              <ion-select-option v-for="source in sources" :key="source.id" :value="source.id">
                {{ source.name }}
              </ion-select-option>
            </ion-select>
          </ion-item>

          <ion-item v-if="viewMode === 'branches'" lines="none" class="filter-dropdown">
            <ion-select
              v-model="selectedDistrict"
              interface="popover"
              :placeholder="'全部地區'"
              @ionChange="handleDistrictChange"
            >
              <ion-select-option value="all">全部地區</ion-select-option>
              <ion-select-option v-for="district in districts" :key="district" :value="district">
                {{ district }}
              </ion-select-option>
            </ion-select>
          </ion-item>

          <!-- Organization Category Filter -->
          <ion-item v-if="viewMode === 'organizations'" lines="none" class="filter-dropdown">
            <ion-select
              v-model="selectedOrgCategory"
              interface="popover"
              :placeholder="'全部類別'"
              @ionChange="handleOrgCategoryChange"
            >
              <ion-select-option value="all">全部類別</ion-select-option>
              <ion-select-option v-for="category in organizationCategories" :key="category.id" :value="category.id">
                {{ category.name }}
              </ion-select-option>
            </ion-select>
          </ion-item>
        </div>

        <ion-buttons slot="end">
          <ion-button v-if="viewMode == 'organizations' && canCreateOrganization" @click="showCreateOrgModal = true">
            <ion-icon :icon="addOutline"></ion-icon>
            <span class="add-label">新增組織</span>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <div v-if="isLoading" class="loading-container">
        <ion-spinner></ion-spinner>
        <p>載入中...</p>
      </div>

      <div v-else class="page-container">
        <!-- Branches View -->
        <div v-if="viewMode === 'branches'" class="branches-container ion-padding">
          <div class="branches-grid">
            <div v-for="branch in filteredBranches" :key="branch.id" class="branch-card-wrapper">
              <BranchCard
                :branch="branch"
                :is-member="isBranchMember(branch.id)"
                :show-join-button="true"
              />
              <div v-if="authStore.isAuthenticated" class="branch-actions">
                <ion-button fill="clear" @click.prevent.stop="toggleBranchFavorite(branch)" class="action-button">
                  <ion-icon :icon="isBranchFavorite(branch) ? heart : heartOutline" />
                </ion-button>
              </div>
            </div>
          </div>

          <!-- Empty State for Branches -->
          <div v-if="filteredBranches.length === 0 && !isLoading" class="empty-state">
            <ion-icon :icon="searchOutline" class="empty-icon"></ion-icon>
            <h3>沒有找到符合條件的分會</h3>
            <p>請嘗試其他搜尋條件</p>
          </div>
        </div>

        <!-- Organizations View -->
        <div v-else class="organizations-container ion-padding">
          <div class="organizations-grid">
            <div v-for="organization in filteredOrganizations" :key="organization.id" class="organization-card-wrapper">
              <OrganizationCard
                :organization="organization"
                :is-member="false"
                :show-join-button="true"
              />
              <div v-if="authStore.isAuthenticated" class="organization-actions">
                <ion-button fill="clear" @click.prevent.stop="toggleOrganizationFavorite(organization)" class="action-button">
                  <ion-icon :icon="isOrganizationFavorite(organization) ? heart : heartOutline" />
                </ion-button>
              </div>
            </div>
          </div>

          <!-- Empty State for Organizations -->
          <div v-if="filteredOrganizations.length === 0 && !isLoading" class="empty-state">
            <ion-icon :icon="searchOutline" class="empty-icon"></ion-icon>
            <h3>沒有找到符合條件的組織</h3>
            <p>請嘗試其他搜尋條件</p>
          </div>
        </div>
      </div>

      <!-- Create Organization Modal -->
      <OrganizationFormModal
        :is-open="showCreateOrgModal"
        @close="showCreateOrgModal = false"
        @created="handleOrganizationCreated"
      />

      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonContent,
  IonSearchbar,
  IonButtons,
  IonButton,
  IonToast,
  IonSegment,
  IonSegmentButton,
  IonLabel,
  IonPopover,
  IonList,
  IonItem,
  IonSpinner,
  IonIcon,
  IonSelect,
  IonSelectOption,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  searchOutline,
  heart,
  heartOutline,
  options,
  checkmark,
  addOutline,
} from 'ionicons/icons';
import BranchCard from '@/components/BranchCard.vue';
import OrganizationCard from '@/components/OrganizationCard.vue';
import OrganizationFormModal from '@/components/OrganizationFormModal.vue';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';
import { useOrganizationStore } from '@/stores/organization';
import { useRouter, useRoute } from 'vue-router';
import { supabase } from '@/lib/supabase';

const authStore = useAuthStore();
const organizationStore = useOrganizationStore();
const userStore = useUserStore();
const currentUser = computed(() => userStore.currentUser);
const router = useRouter();
const route = useRoute();

// Common state
const branches = ref<any[]>([]);
const organizations = ref<any[]>([]);
const favoriteBranches = ref(new Set<string>());
const favoriteOrganizations = ref(new Set<string>());
const isLoading = ref(true);
const searchQuery = ref('');
const toastMessage = ref('');
const selectedCategory = ref('all');
const selectedSource = ref('all');
const selectedDistrict = ref('all');
const sortOption = ref('newest');
const viewMode = ref('branches');
const showCreateOrgModal = ref(false);
const showSortPopover = ref(false);
const popoverEvent = ref<Event | null>(null);
const categories = ref<any[]>([]);
const sources = ref<any[]>([]);
const organizationCategories = ref<any[]>([]);
const categoryMap = ref(new Map<string, string>());
const selectedOrgCategory = ref('all');

// List of districts in Hong Kong
const districts = [
  '中西區', '灣仔區', '東區', '南區',
  '油尖旺區', '深水埗區', '九龍城區', '黃大仙區', '觀塘區',
  '葵青區', '荃灣區', '屯門區', '元朗區',
  '北區', '大埔區', '沙田區', '西貢區', '離島區'
];

// Check if user can create organizations (is a branch president)
const canCreateOrganization = computed(() => {
  return authStore.isAuthenticated && currentUser.value?.role == 'president';
});

// Filtered branches based on search, category, and district
const filteredBranches = computed(() => {
  let result = branches.value;

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(branch =>
      branch.name.toLowerCase().includes(query) ||
      (branch.description && branch.description.toLowerCase().includes(query))
    );
  }

  // Filter by category
  if (selectedCategory.value !== 'all') {
    result = result.filter(branch => branch.category_id === selectedCategory.value);
  }

  // Filter by source
  if (selectedSource.value !== 'all') {
    result = result.filter(branch => branch.source_id === selectedSource.value);
  }

  // Filter by district
  if (selectedDistrict.value !== 'all') {
    result = result.filter(branch => branch.district === selectedDistrict.value);
  }

  // Sort branches
  switch (sortOption.value) {
    case 'newest':
      result.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      break;
    case 'oldest':
      result.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
      break;
    case 'popular':
      result.sort((a, b) => (b.member_count || 0) - (a.member_count || 0));
      break;
    case 'name':
      result.sort((a, b) => a.name.localeCompare(b.name, 'zh-HK'));
      break;
  }

  return result;
});

// Filtered organizations based on search and category
const filteredOrganizations = computed(() => {
  let result = organizations.value;

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(organization =>
      organization.name.toLowerCase().includes(query) ||
      (organization.description && organization.description.toLowerCase().includes(query))
    );
  }

  // Filter by category
  if (selectedOrgCategory.value !== 'all') {
    result = result.filter(organization => organization.category_id === selectedOrgCategory.value);
  }

  // Sort organizations
  switch (sortOption.value) {
    case 'newest':
      result.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      break;
    case 'oldest':
      result.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
      break;
    case 'popular':
      result.sort((a, b) => (b.member_count || 0) - (a.member_count || 0));
      break;
    case 'name':
      result.sort((a, b) => a.name.localeCompare(b.name, 'zh-HK'));
      break;
  }

  return result;
});

// Handle search input
const handleSearch = (event: CustomEvent) => {
  searchQuery.value = event.detail.value || '';
};

// Handle category change
const handleCategoryChange = () => {
  // Reset other filters when category changes if needed
  // selectedSource.value = 'all';
  // selectedDistrict.value = 'all';
};

// Handle source change
const handleSourceChange = () => {
  // Reset other filters when source changes if needed
  // selectedCategory.value = 'all';
  // selectedDistrict.value = 'all';
};

// Handle district change
const handleDistrictChange = () => {
  // Reset other filters when district changes if needed
  // selectedCategory.value = 'all';
  // selectedSource.value = 'all';
};

// Handle organization category change
const handleOrgCategoryChange = () => {
  // Reset other filters when organization category changes if needed
};

// Present sort options popover
const presentSortOptions = (event: Event) => {
  popoverEvent.value = event;
  showSortPopover.value = true;
};

// Set sort option
const setSortOption = (option: string) => {
  sortOption.value = option;
  showSortPopover.value = false;
};

// Check if branch is a favorite
const isBranchFavorite = (branch: any) => {
  return favoriteBranches.value.has(branch.id);
};

// Check if organization is a favorite
const isOrganizationFavorite = (organization: any) => {
  return favoriteOrganizations.value.has(organization.id);
};

// Check if user is a member of a branch
const isBranchMember = (branchId: string) => {
  if (!authStore.isAuthenticated || !authStore.currentUser) return false;

  // Check if user is a member of the branch
  const branch = branches.value.find(b => b.id === branchId);
  if (!branch || !branch.branch_members) return false;

  return branch.branch_members.some(
    (member: any) => member.user_id === authStore.currentUser?.id
  );
};

// Toggle branch favorite
const toggleBranchFavorite = async (branch: any) => {
  if (!authStore.isAuthenticated) {
    toastMessage.value = '請先登入';
    return;
  }

  try {
    if (favoriteBranches.value.has(branch.id)) {
      // Remove from favorites in database
      const { error } = await supabase
        .from('user_liked_branches')
        .delete()
        .eq('user_id', authStore.currentUser.id)
        .eq('branch_id', branch.id);

      if (error) throw error;

      // Update local state
      favoriteBranches.value.delete(branch.id);
      toastMessage.value = '已從收藏移除';
    } else {
      // Add to favorites in database
      const { error } = await supabase
        .from('user_liked_branches')
        .insert({
          user_id: authStore.currentUser.id,
          branch_id: branch.id
        });

      if (error) throw error;

      // Update local state
      favoriteBranches.value.add(branch.id);
      toastMessage.value = '已加入收藏';
    }
  } catch (error) {
    console.error('Error toggling branch favorite:', error);
    toastMessage.value = '操作失敗，請稍後再試';
  }
};

// Toggle organization favorite
const toggleOrganizationFavorite = async (organization: any) => {
  if (!authStore.isAuthenticated) {
    toastMessage.value = '請先登入';
    return;
  }

  try {
    if (favoriteOrganizations.value.has(organization.id)) {
      // Remove from favorites in database
      const { error } = await supabase
        .from('user_liked_organizations')
        .delete()
        .eq('user_id', authStore.currentUser.id)
        .eq('organization_id', organization.id);

      if (error) throw error;

      // Update local state
      favoriteOrganizations.value.delete(organization.id);
      toastMessage.value = '已從收藏移除';
    } else {
      // Add to favorites in database
      const { error } = await supabase
        .from('user_liked_organizations')
        .insert({
          user_id: authStore.currentUser.id,
          organization_id: organization.id
        });

      if (error) throw error;

      // Update local state
      favoriteOrganizations.value.add(organization.id);
      toastMessage.value = '已加入收藏';
    }
  } catch (error) {
    console.error('Error toggling organization favorite:', error);
    toastMessage.value = '操作失敗，請稍後再試';
  }
};

// Handle organization created
const handleOrganizationCreated = (newOrganization: any) => {
  organizations.value.unshift(newOrganization);
  showCreateOrgModal.value = false;
  toastMessage.value = '組織創建成功';
};

// Load branches
const loadBranches = async () => {
  try {
    isLoading.value = true;

    // Load branch categories
    const { data: categoriesData, error: categoriesError } = await supabase
      .from('branch_categories')
      .select('*')
      .eq('is_approved', true)
      .order('sort_order', { ascending: true });

    if (categoriesError) throw categoriesError;
    categories.value = categoriesData || [];

    // Create category map for quick lookups
    categoryMap.value = new Map(
      categoriesData.map((c: any) => [c.id, c.title])
    );

    // Load branch sources
    const { data: sourcesData, error: sourcesError } = await supabase
      .from('branch_sources')
      .select('*')
      .eq('is_approved', true)
      .order('sort_order', { ascending: true });

    if (sourcesError) throw sourcesError;
    sources.value = sourcesData || [];

    // Load branches with owner information
    const { data, error } = await supabase
      .from('branches')
      .select(`
        *,
        branch_categories (
          id,
          title
        ),
        branch_sources (
          id,
          name
        ),
        owner:owner_id (
          id,
          full_name,
          username
        )
      `)
      .order('created_at', { ascending: false });

    if (error) throw error;

    // Process branch data to add owner information
    branches.value = data || [];

    // Load user's liked branches if authenticated
    if (authStore.isAuthenticated && authStore.currentUser?.id) {
      const { data: likedData, error: likedError } = await supabase
        .from('user_liked_branches')
        .select('branch_id')
        .eq('user_id', authStore.currentUser.id);

      if (!likedError && likedData) {
        favoriteBranches.value = new Set(likedData.map((item: any) => item.branch_id));
      }
    }
  } catch (error) {
    console.error('Error loading branches:', error);
    toastMessage.value = '載入分會資料時發生錯誤';
  } finally {
    isLoading.value = false;
  }
};

// Load organizations
const loadOrganizations = async () => {
  try {
    isLoading.value = true;

    // Load organization categories
    const { data: orgCategoriesData, error: orgCategoriesError } = await supabase
      .from('organization_categories')
      .select('*')
      .eq('is_approved', true)
      .order('sort_order', { ascending: true });

    if (orgCategoriesError) throw orgCategoriesError;
    organizationCategories.value = orgCategoriesData || [];

    // Load organizations with category information
    const { data: organizationsData, error: organizationsError } = await supabase
      .from('organizations')
      .select(`
        *,
        owner:owner_id (
          id,
          full_name,
          username
        ),
        organization_categories (
          id,
          name
        )
      `)
      .order('created_at', { ascending: false });

    if (organizationsError) throw organizationsError;
    organizations.value = organizationsData || [];

    // Load user's liked organizations if authenticated
    if (authStore.isAuthenticated && authStore.currentUser?.id) {
      const { data: likedData, error: likedError } = await supabase
        .from('user_liked_organizations')
        .select('organization_id')
        .eq('user_id', authStore.currentUser.id);

      if (!likedError && likedData) {
        favoriteOrganizations.value = new Set(likedData.map((item: any) => item.organization_id));
      }
    }
  } catch (error) {
    console.error('Error loading organizations:', error);
    toastMessage.value = '載入組織資料時發生錯誤';
  } finally {
    isLoading.value = false;
  }
};

// Handle view mode change
const handleViewModeChange = async (event: CustomEvent) => {
  const newMode = event.detail.value;

  // Update URL without triggering navigation
  const newPath = newMode === 'branches' ? '/branches' : '/organizations';

  // Use router.replace instead of history.replaceState to ensure proper history management
  // This will update the URL without adding a new entry to the history stack
  router.replace(newPath);

  // Always load data when switching views to ensure fresh data
  if (newMode === 'branches') {
    await loadBranches();
  } else {
    await loadOrganizations();
  }
};

// Initialize view mode based on route and load data
onMounted(async () => {
  const path = route.path;
  if (path === '/organizations') {
    viewMode.value = 'organizations';
    await loadOrganizations();
  } else {
    viewMode.value = 'branches';
    await loadBranches();
  }
});

// Watch for route changes to update view mode only (no navigation)
watch(() => route.path, (newPath) => {
  // Only update the view mode without triggering data loading
  // This prevents double-loading when using browser back/forward buttons
  if (newPath === '/organizations') {
    viewMode.value = 'organizations';
  } else if (newPath === '/branches') {
    viewMode.value = 'branches';
  }
});

// Load data when the view is entered
onIonViewDidEnter(async () => {
  // Reset filter on every time page enter
  selectedSource.value = 'all';
  selectedCategory.value = 'all';
  selectedDistrict.value = 'all';

  // Always refresh data when the view is entered
  // This ensures we have fresh data when navigating back to this page
  if (viewMode.value === 'branches') {
    await loadBranches();
  } else {
    await loadOrganizations();
  }

  // Make sure the URL matches the current view mode
  const currentPath = router.currentRoute.value.path;
  const expectedPath = viewMode.value === 'branches' ? '/branches' : '/organizations';

  if (currentPath !== expectedPath) {
    // Update URL without triggering navigation
    router.replace(expectedPath);
  }
});
</script>

<style scoped>
.filter-toolbar {
  --padding-top: 8px;
  --padding-bottom: 8px;
}

.custom-searchbar {
  --border-radius: 20px;
  --background: var(--ion-background-color);
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0 8px;
}

/* Add Organization Button Styling */
ion-button .add-label {
  margin-left: 4px;
  font-size: 14px;
}

.filter-options {
  --min-height: 48px;
  --padding-top: 0;
  --padding-bottom: 0;
}

.filter-dropdowns {
  display: flex;
  width: 100%;
  overflow-x: auto;
}

.filter-dropdown {
  --min-height: 48px;
  flex-shrink: 0;
  min-width: 150px;
}

.loading-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.branches-container,
.organizations-container {
  width: 100%;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 2rem 0 1rem;
  color: var(--ion-text-color);
}

.branches-grid,
.organizations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.branch-card-wrapper,
.organization-card-wrapper {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  background: white;
}

.branch-card-wrapper:hover,
.organization-card-wrapper:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.branch-actions,
.organization-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
}

.action-button {
  --padding-start: 8px;
  --padding-end: 8px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  margin: 0;
  --background: rgba(255, 255, 255, 0.8);
  --color: var(--ion-color-danger);
  --border-radius: 50%;
}

.empty-state {
  padding: 3rem 1rem;
  text-align: center;
  color: var(--ion-color-medium);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

@media (max-width: 576px) {
  .branches-grid,
  .organizations-grid {
    grid-template-columns: 1fr;
  }
}
</style>
