-- Drop the registration policy as we'll handle it via trigger
DROP POLICY IF EXISTS "Enable insert for registration" ON users;

-- <PERSON>reate function to handle user profile creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  new_role text;
  new_industry text;
  new_company_name text;
  new_referrer_id uuid;
  referrer_exists boolean;
BEGIN
  -- Extract and validate role
  new_role := COALESCE(NEW.raw_user_meta_data->>'role', 'free');
  IF new_role NOT IN ('free', 'merchant', 'president') THEN
    new_role := 'free';
  END IF;

  -- Extract optional fields
  new_industry := NEW.raw_user_meta_data->>'industry';
  new_company_name := NEW.raw_user_meta_data->>'company_name';
  new_referrer_id := (NEW.raw_user_meta_data->>'referrer_id')::uuid;

  -- Validate industry and company_name based on role
  IF new_role IN ('merchant', 'president') THEN
    IF new_industry IS NULL OR new_company_name IS NULL THEN
      RAISE EXCEPTION 'Industry and company name are required for merchant and president roles';
    END IF;
  END IF;

  -- Validate referrer exists if provided
  IF new_referrer_id IS NOT NULL THEN
    SELECT EXISTS (
      SELECT 1 FROM public.users WHERE id = new_referrer_id
    ) INTO referrer_exists;

    IF NOT referrer_exists THEN
      RAISE EXCEPTION 'Referrer with ID % does not exist', new_referrer_id;
    END IF;
  END IF;

  -- Validate username is unique
  IF EXISTS (
    SELECT 1 FROM public.users WHERE username = NEW.raw_user_meta_data->>'username'
  ) THEN
    RAISE EXCEPTION 'Username % is already taken', NEW.raw_user_meta_data->>'username';
  END IF;

  -- Validate phone number format
  IF NEW.raw_user_meta_data->>'phone' IS NULL OR length(NEW.raw_user_meta_data->>'phone') < 8 THEN
    RAISE EXCEPTION 'Invalid phone number format';
  END IF;

  -- Insert new user with all fields
  INSERT INTO public.users (
    id,
    username,
    full_name,
    email,
    phone,
    role,
    referrer_id,
    industry,
    company_name,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'username',
    NEW.raw_user_meta_data->>'full_name',
    NEW.email,
    NEW.raw_user_meta_data->>'phone',
    new_role,
    new_referrer_id,
    new_industry,
    new_company_name,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
  );
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error details
    RAISE LOG 'Error in handle_new_user: %', SQLERRM;
    -- Re-raise the error
    RAISE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing trigger if exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create trigger on auth.users
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Update RLS policies
DROP POLICY IF EXISTS "Enable read access to own profile" ON users;
DROP POLICY IF EXISTS "Enable update access to own profile" ON users;

CREATE POLICY "Enable read access to own profile"
  ON users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Enable update access to own profile"
  ON users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Add policy to allow reading user emails for registration validation
CREATE POLICY "Allow email checks during registration"
  ON users
  FOR SELECT
  TO anon, authenticated
  USING (true);