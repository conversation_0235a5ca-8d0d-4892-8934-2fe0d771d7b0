import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { VitePWA } from 'vite-plugin-pwa';
import path from 'path';

export default defineConfig({
  plugins: [
    vue(),
    VitePWA({
      // Use autoUpdate for automatic background updates
      registerType: 'autoUpdate',
      includeAssets: ['favicon.ico', 'apple-touch-icon.png', 'mask-icon.svg'],
      manifest: {
        name: '商聯思維',
        short_name: 'Synerbiz',
        description: `智能商務媒合系統，破解［ 客完不足｜成本黑洞｜人物閒置]，商聯思維將為商家帶來超過10種不同好處與便利性，助你業務上升。平台透過最新的市場商業模組串連設計，幫助市場上各商家及業務人士打造生易與生意之間的橋樑連結，將不同的圈子，不同的資源串連整合同時運用不同的工具設計解決到各商家及業務人士市場上所會面對到的煩惱及痛點。`,
        theme_color: '#ffffff',
        icons: [
          {
            src: 'pwa-192x192.png',
            sizes: '192x192',
            type: 'image/png'
          },
          {
            src: 'pwa-512x512.png',
            sizes: '512x512',
            type: 'image/png'
          }
        ]
      },
      workbox: {
        // Force update on new deployments
        clientsClaim: true,
        skipWaiting: true,
        // Don't cache dynamic imports for too long
        runtimeCaching: [
          {
            // Match all navigation requests
            urlPattern: ({ request }) => request.mode === 'navigate',
            handler: 'NetworkFirst',
            options: {
              cacheName: 'navigation-cache'
            }
          },
          {
            // Match all JavaScript files including dynamic imports
            urlPattern: /\.js$/,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'js-cache',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 60 * 60 // 1 hour
              }
            }
          },
          {
            // Match all other assets
            urlPattern: /\.(?:png|jpg|jpeg|svg|gif|css|ico)$/,
            handler: 'StaleWhileRevalidate',
            options: {
              cacheName: 'assets-cache',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 60 * 60 * 24 * 7 // 7 days
              }
            }
          }
        ]
      }
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  // Add build options to better handle caching
  build: {
    // Add timestamp to chunk filenames to ensure unique names on each build
    rollupOptions: {
      output: {
        // Use content hash for chunks to ensure proper caching
        chunkFileNames: 'assets/[name].[hash].js',
        entryFileNames: 'assets/[name].[hash].js',
        assetFileNames: 'assets/[name].[hash].[ext]'
      }
    }
  }
});