import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "npm:@supabase/supabase-js@2.39.8";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface NotificationData {
  messageId?: string;
  conversationId?: string;
  senderId?: string;
  content?: string;
  messageType?: string;
  processQueue?: boolean;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    console.log('Starting chat notification process...');

    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get notification data from request
    const requestData: NotificationData = await req.json();
    console.log('Request data:', requestData);

    // Check if this is a queue processing request
    if (requestData.processQueue) {
      return await processNotificationQueue(supabaseClient);
    }

    // Handle single notification
    const { messageId, conversationId, senderId, content, messageType } = requestData;
    if (!messageId || !conversationId || !senderId) {
      throw new Error('Missing required notification data');
    }

    return await sendSingleNotification(supabaseClient, {
      messageId,
      conversationId,
      senderId,
      content: content || '',
      messageType: messageType || 'text'
    });
  } catch (error) {
    console.error('Function error:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
      }
    );
  }
});

async function processNotificationQueue(supabaseClient: any) {
  console.log('Processing notification queue...');

  // Get unprocessed notifications
  const { data: notifications, error } = await supabaseClient
    .from('chat_notification_queue')
    .select('*')
    .eq('processed', false)
    .order('created_at', { ascending: true })
    .limit(10); // Process in batches

  if (error) {
    console.error('Error fetching notification queue:', error);
    throw new Error('Failed to fetch notification queue');
  }

  if (!notifications || notifications.length === 0) {
    console.log('No notifications to process');
    return new Response(
      JSON.stringify({ success: true, processed: 0 }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
      }
    );
  }

  let processedCount = 0;
  const errors: string[] = [];

  for (const notification of notifications) {
    try {
      await sendNotificationToUser(
        notification.recipient_id,
        notification.sender_id,
        notification.conversation_id,
        notification.content,
        notification.message_type
      );

      // Mark as processed
      await supabaseClient
        .from('chat_notification_queue')
        .update({ processed: true })
        .eq('id', notification.id);

      processedCount++;
      console.log(`Processed notification ${notification.id}`);
    } catch (error) {
      console.error(`Error processing notification ${notification.id}:`, error);
      errors.push(`Notification ${notification.id}: ${error.message}`);
    }
  }

  return new Response(
    JSON.stringify({
      success: true,
      processed: processedCount,
      errors: errors.length > 0 ? errors : undefined
    }),
    {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json',
      },
    }
  );
}

async function sendSingleNotification(supabaseClient: any, data: {
  messageId: string;
  conversationId: string;
  senderId: string;
  content: string;
  messageType: string;
}) {
  const { messageId, conversationId, senderId, content, messageType } = data;

  // Get conversation details to find the recipient
  const { data: conversation, error: conversationError } = await supabaseClient
    .from('conversations')
    .select(`
      *,
      participant_1:users!conversations_participant_1_id_fkey(id, full_name),
      participant_2:users!conversations_participant_2_id_fkey(id, full_name)
    `)
    .eq('id', conversationId)
    .single();

  if (conversationError || !conversation) {
    console.error('Error fetching conversation:', conversationError);
    throw new Error('Conversation not found');
  }

  // Determine the recipient (the participant who is not the sender)
  const recipient = conversation.participant_1.id === senderId
    ? conversation.participant_2
    : conversation.participant_1;

  // Get sender details
  const { data: sender, error: senderError } = await supabaseClient
    .from('users')
    .select('id, full_name')
    .eq('id', senderId)
    .single();

  if (senderError || !sender) {
    console.error('Error fetching sender:', senderError);
    throw new Error('Sender not found');
  }

  await sendNotificationToUser(recipient.id, senderId, conversationId, content, messageType, sender.full_name);

  return new Response(
    JSON.stringify({ success: true }),
    {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json',
      },
    }
  );
}

async function sendNotificationToUser(
  recipientId: string,
  senderId: string,
  conversationId: string,
  content: string,
  messageType: string,
  senderName?: string
) {
  // Get sender name if not provided
  if (!senderName) {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { data: sender } = await supabaseClient
      .from('users')
      .select('full_name')
      .eq('id', senderId)
      .single();

    senderName = sender?.full_name || 'Unknown User';
  }

  // Prepare notification content
  let notificationTitle = senderName;
  let notificationBody = '';

  if (messageType === 'image') {
    notificationBody = '發送了一張圖片';
  } else {
    // Truncate long messages
    notificationBody = content.length > 50 ? content.substring(0, 50) + '...' : content;
  }

  // Prepare OneSignal notification payload
  const oneSignalPayload = {
    app_id: Deno.env.get('ONESIGNAL_APP_ID'),
    include_external_user_ids: [recipientId],
    headings: { en: notificationTitle },
    contents: { en: notificationBody },
    data: {
      conversationId: conversationId,
      senderId: senderId,
      type: 'chat_message'
    },
    // iOS specific settings
    ios_badgeType: 'Increase',
    ios_badgeCount: 1,
    // Android specific settings
    android_channel_id: 'chat_messages'
  };

  console.log('Sending OneSignal notification to user:', recipientId);

  // Send notification via OneSignal API
  const oneSignalResponse = await fetch('https://onesignal.com/api/v1/notifications', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Basic ${Deno.env.get('ONESIGNAL_REST_API_KEY')}`,
    },
    body: JSON.stringify(oneSignalPayload),
  });

  const oneSignalResult = await oneSignalResponse.json();

  if (!oneSignalResponse.ok) {
    console.error('OneSignal API error:', oneSignalResult);
    throw new Error(`Failed to send notification: ${JSON.stringify(oneSignalResult)}`);
  }

  console.log('Notification sent successfully:', oneSignalResult);
  return oneSignalResult;
}
